// 配置管理模块
class ConfigManager {
    constructor() {
        this.configVersion = '1.1.0'; // 配置版本，用于检测更新
        this.defaultConfig = {
            service: '', // 取消默认使用百度OCR
            configVersion: this.configVersion, // 配置版本标识
            baidu: {
                apiKey: '',
                secretKey: '',
                // 百度翻译API配置（独立配置）
                translateApiKey: '',
                translateSecretKey: '',
                translateTermIds: '' // 术语库ID（可选，多个用逗号分隔）
            },
            tencent: {
                secretId: '',
                secretKey: '',
                region: 'ap-beijing',
                // 腾讯翻译配置（使用OCR相同的密钥）
                translateRegion: 'ap-beijing',
                translateTermRepoIDList: [],      // 术语库ID列表
                translateSentRepoIDList: [],      // 例句库ID列表
                translateUntranslatedText: ''     // 不翻译文本
            },
            aliyun: {
                accessKey: '',
                accessSecret: '',
                region: 'cn-shanghai',
                // 阿里云翻译配置（使用OCR相同的密钥）
                translateRegion: 'cn-shanghai',
                translateContext: ''              // 上下文信息（可选）
            },
            // 识别模式配置
            recognitionModes: {
                text: {
                    service: '',
                    model: null,
                    prompt: '请准确识别图片中的的所有文字内容，直接输出文字，尽可能保留原文的排版和结构。始终将识别的精确性放在首位。严格遵守“只回复识别结果”的指示，不添加任何解释、提示。'
                },
                table: {
                    service: '',
                    model: null,
                    prompt: '请准确识别图片中的表格内容，以Markdown表格格式输出，保持原有的行列结构。始终将识别的精确性放在首位。严格遵守“只回复识别结果”的指示，不添加任何解释、提示。'
                },
                formula: {
                    service: '',
                    model: null,
                    prompt: '请准确识别图片中的的数学公式，以LaTeX格式输出，如果有多个公式请分行显示。始终将识别的精确性放在首位。严格遵守“只回复识别结果”的指示，不添加任何解释、提示。'
                },
                markdown: {
                    service: '',
                    model: null,
                    prompt: '请准确识别图片中的所有内容，以Markdown格式输出，尽可能保留原文的排版和列表结构，包括标题、段落、列表、表格等结构。始终将识别的精确性放在首位。严格遵守“只回复识别结果”的指示，不添加任何解释、提示。'
                }
            },
            // AI平台配置 - model字段表示"当前选中模型"，用户需主动添加和选择
            openai: {
                model: '', // 当前选中的模型ID，空表示未选择
                useCustomModel: false,
                customModel: '',
                apiKey: '',
                baseUrl: 'https://api.openai.com/v1',
                maxTokens: 1000
            },
            anthropic: {
                model: '', // 当前选中的模型ID，空表示未选择
                useCustomModel: false,
                customModel: '',
                apiKey: '',
                baseUrl: 'https://api.anthropic.com/v1',
                maxTokens: 1000
            },
            google: {
                model: '', // 当前选中的模型ID，空表示未选择
                apiKey: '',
                baseUrl: 'https://generativelanguage.googleapis.com',
                modelList: []
            },
            alibaba: {
                model: '', // 当前选中的模型ID，空表示未选择
                useCustomModel: false,
                customModel: '',
                apiKey: '',
                baseUrl: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
                maxTokens: 1000,
                modelNameMap: {
                    // Qwen-MT翻译专用模型
                    'qwen-mt-plus': 'Qwen-MT Plus - 旗舰翻译模型',
                    'qwen-mt-turbo': 'Qwen-MT Turbo - 快速翻译模型',
                    // 视觉OCR模型
                    'qwen-vl-max': 'Qwen-VL Max - 最强视觉模型',
                    'qwen-vl-plus': 'Qwen-VL Plus - 高性能视觉模型',
                    'qwen-vl-ocr-latest': 'Qwen-VL OCR Latest - 最新OCR模型',
                    'qwen-vl-ocr-2025-04-13': 'Qwen-VL OCR (2025.04.13)',
                    'qwen2-vl-7b-instruct': 'Qwen2-VL 7B Instruct',
                    'qwen2-vl-72b-instruct': 'Qwen2-VL 72B Instruct',
                    'qwen2.5-vl-7b-instruct': 'Qwen2.5-VL 7B Instruct',
                    'qwen2.5-vl-72b-instruct': 'Qwen2.5-VL 72B Instruct'
                }
            },
            bytedance: {
                model: '', // 当前选中的模型ID，空表示未选择
                useCustomModel: false,
                customModel: '',
                apiKey: '', // 聊天API的API Key
                baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
                maxTokens: 1000,
                // 新增的模型管理API配置
                accessKey: '', // Access Key ID
                secretKey: '' // Secret Access Key
            },
            ocrpro: {
                model: '', // 当前选中的模型ID，空表示未选择
                useCustomModel: false,
                customModel: '',
                maxTokens: 1000,
                modelList: [
                    'gemini-2.5-flash-lite-preview-06-17',
                    'gemini-2.5-flash'
                ], // OCR Pro使用预设模型列表
                modelNameMap: {
                    'gemini-2.5-flash-lite-preview-06-17': 'Gemini 2.5 Flash-Lite Preview 06-17',
                    'gemini-2.5-flash': 'Gemini 2.5 Flash'
                }
            },
            utools: {
                model: '', // 当前选中的模型ID，空表示未选择
                useCustomModel: false,
                customModel: '',
                maxTokens: 1000
            },
            custom: {
                model: '',
                useCustomModel: false,
                customModel: '',
                apiKey: '',
                baseUrl: '',
                maxTokens: 1000
            },
            ui: {
                theme: 'auto',
                removeLinebreaks: false,
                autoHide: true,
                copyAfterOCR: false,
                autoCopyAfterTranslate: false, // 翻译后自动复制，默认关闭
                showNotification: true,
                autoReOcr: false,
                enableHistory: true,
                historyMaxCount: 100,
                autoClose: false,
                autoModelSwitch: false, // 自动故障恢复开关，默认关闭
                autoTranslate: false, // 自动翻译开关，默认关闭
                autoCloseOCR: false, // OCR页面复制后自动关闭，默认关闭
                autoCloseTranslate: false, // 翻译页面复制后自动关闭，默认关闭
                autoCleanOCR: true, // OCR页面自动清空开关，默认开启
                autoCleanTranslate: true, // 翻译页面自动清空开关，默认开启
                autoTranslateImageTranslate: true, // 图片翻译页面自动翻译开关，默认开启
                autoCleanImageTranslate: true // 图片翻译页面自动清空开关，默认开启
            },
            // 翻译模型配置
            translateModels: [
                // 默认添加Qwen-MT模型
                {
                    service: 'alibaba',
                    model: 'qwen-mt-turbo',
                    addedAt: Date.now(),
                    type: 'ai',
                    isDefault: true
                },
                {
                    service: 'alibaba',
                    model: 'qwen-mt-plus',
                    addedAt: Date.now(),
                    type: 'ai',
                    isDefault: true
                }
            ],

            // 用户选择的翻译模型列表（持久化存储）
            selectedTranslateModels: [],

            // 模型服务商顺序配置
            // 影响范围：
            // 1. 模型服务页面中服务商列表的显示顺序
            // 2. 模型筛选和选择时的默认排序（getAvailableModelsForConfig）
            // 3. 识别模式模型选择菜单中的服务商排序
            // 4. 主页面模型选择下拉菜单中的平台分组顺序
            // 5. 翻译模型添加弹窗中的模型列表显示顺序
            serviceOrder: [
                'ocrpro',     // OCR Pro
                'baidu',      // 百度智能云
                'tencent',    // 腾讯云
                'aliyun',     // 阿里云
                'openai',     // OpenAI
                'anthropic',  // Anthropic
                'google',     // Gemini
                'alibaba',    // 阿里云百炼
                'bytedance',  // 火山引擎
                'utools'      // uTools AI
            ],
            shortcuts: {
                // 基础操作快捷键
                copyResult: 'Ctrl+C',
                takeScreenshot: '',
                reRecognize: '',
                clearResult: '',
                triggerTranslation: '',           // 触发翻译功能

                // 页面操作
                openSettingsPage: '',             // 基础配置
                openHistoryPage: '',              // 历史记录页面
                openOCRPage: '',                  // OCR页面
                openTranslationPage: '',          // 翻译页面
                openModelServicePage: '',         // 模型服务页面

                // 功能切换
                switchToText: '',                 // 切换到文字识别
                switchToTable: '',               // 切换到表格识别
                switchToFormula: '',             // 切换到公式识别
                switchToMarkdown: '',            // 切换到Markdown识别
                toggleTheme: '',                  // 主题切换
                toggleLineBreakMode: '',          // 换行符模式切换
                switchTranslationModel: '',       // 翻译模型切换
                switchHistoryCategory: '',        // 历史记录分类切换

                // 保留原有快捷键（向后兼容）
                openSettings: '',
                backToMain: ''
            }
        };
    }

    // 获取配置
    getConfig() {
        const config = window.ocrAPI.db.get('ocr-config');
        const mergedConfig = config ? this.deepMerge(this.defaultConfig, config) : this.defaultConfig;

        // 迁移旧的提示词配置到新的识别模式结构
        this.migratePromptConfig(mergedConfig);

        // 检查配置版本并处理更新
        this.handleConfigVersionUpdate(mergedConfig);

        // 确保服务商顺序包含所有默认服务商
        this.ensureServiceOrderComplete(mergedConfig);

        return mergedConfig;
    }

    // 确保服务商顺序配置包含所有默认服务商
    ensureServiceOrderComplete(config) {
        if (!config.serviceOrder) {
            config.serviceOrder = [...this.defaultConfig.serviceOrder];
            return;
        }

        const currentOrder = config.serviceOrder;
        const defaultOrder = this.defaultConfig.serviceOrder;

        // 检查是否有新的服务商需要添加
        const missingServices = defaultOrder.filter(service => !currentOrder.includes(service));

        if (missingServices.length > 0) {
            // 将新服务商添加到适当位置
            const updatedOrder = [...currentOrder];

            missingServices.forEach(service => {
                // 找到在默认配置中的位置，并插入到相应位置
                const defaultIndex = defaultOrder.indexOf(service);
                let insertIndex = updatedOrder.length;

                // 寻找合适的插入位置
                for (let i = defaultIndex + 1; i < defaultOrder.length; i++) {
                    const nextService = defaultOrder[i];
                    const existingIndex = updatedOrder.indexOf(nextService);
                    if (existingIndex !== -1) {
                        insertIndex = existingIndex;
                        break;
                    }
                }

                updatedOrder.splice(insertIndex, 0, service);
            });

            config.serviceOrder = updatedOrder;

            // 自动保存更新后的配置
            this.saveConfig(config);
        }
    }

    // 深度合并配置对象
    deepMerge(target, source) {
        const result = { ...target };

        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    // 如果是对象，递归合并
                    result[key] = this.deepMerge(target[key] || {}, source[key]);
                } else {
                    // 如果是基本类型或数组，直接覆盖
                    result[key] = source[key];
                }
            }
        }

        return result;
    }

    // 处理配置版本更新
    handleConfigVersionUpdate(config) {
        const currentVersion = config.configVersion;
        const latestVersion = this.configVersion;

        // 如果版本不匹配，说明代码已更新
        if (!currentVersion || currentVersion !== latestVersion) {

            // 更新配置版本
            config.configVersion = latestVersion;

            // 强制重置服务商顺序以包含新服务商
            config.serviceOrder = [...this.defaultConfig.serviceOrder];

            // 自动保存更新后的配置
            setTimeout(() => {
                this.saveConfig(config);
            }, 100);
        }
    }

    // 迁移旧的提示词配置到新的识别模式结构
    migratePromptConfig(config) {
        // 检查是否需要迁移
        if (!config.recognitionModes) {
            config.recognitionModes = { ...this.defaultConfig.recognitionModes };
        }

        // 如果识别模式配置中没有提示词，但旧的平台配置中有，则进行迁移
        const platforms = ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'utools', 'custom'];
        let needsMigration = false;

        platforms.forEach(platform => {
            if (config[platform]?.prompt) {
                // 将旧的提示词迁移到文字识别模式（作为默认）
                if (!config.recognitionModes.text.prompt ||
                    config.recognitionModes.text.prompt === this.defaultConfig.recognitionModes.text.prompt) {
                    config.recognitionModes.text.prompt = config[platform].prompt;
                    needsMigration = true;
                }

                // 删除旧的prompt字段
                delete config[platform].prompt;
            }
        });

        // 如果进行了迁移，保存配置
        if (needsMigration) {
            this.saveConfig(config);
        }
    }

    // 保存配置
    saveConfig(config) {
        const configToSave = {
            _id: 'ocr-config',
            ...config
        };

        // 检查数据库API是否可用
        if (!window.ocrAPI || !window.ocrAPI.db) {
            return { success: false, error: '数据库API不可用' };
        }

        // 如果配置已存在，需要保留_rev
        let existingConfig;
        try {
            existingConfig = window.ocrAPI.db.get('ocr-config');
        } catch (error) {
            // 继续执行，可能是首次保存
        }

        if (existingConfig && existingConfig._rev) {
            configToSave._rev = existingConfig._rev;
        }

        let result;
        try {
            result = window.ocrAPI.db.put(configToSave);
        } catch (error) {
            return { success: false, error: '数据库保存异常: ' + error.message };
        }

        if (result && result.ok) {
            configToSave._rev = result.rev;
            return { success: true, config: configToSave };
        } else {
            const errorMsg = result ? (result.message || result.error || '保存失败') : '数据库操作返回空结果';
            return { success: false, error: errorMsg };
        }
    }

    // 验证配置
    validateConfig(config) {
        const service = config.service;
        const serviceConfig = config[service];

        if (!serviceConfig) {
            return { valid: false, error: '服务配置不存在' };
        }

        const validators = {
            baidu: () => this.validateTraditionalOCR(serviceConfig, ['apiKey', 'secretKey'], '百度智能云需要API Key和Secret Key'),
            tencent: () => this.validateTraditionalOCR(serviceConfig, ['secretId', 'secretKey'], '腾讯云需要Secret ID和Secret Key'),
            aliyun: () => this.validateTraditionalOCR(serviceConfig, ['accessKey', 'accessSecret'], '阿里云需要Access Key和Access Secret'),
            utools: () => this.validateUtoolsService(serviceConfig),
            default: () => this.validateAIService(serviceConfig, service)
        };

        const validator = validators[service] || validators.default;
        return validator();
    }

    // 验证传统OCR服务
    validateTraditionalOCR(config, requiredFields, errorMessage) {
        const missingFields = requiredFields.filter(field => !config[field]);
        return missingFields.length > 0
            ? { valid: false, error: errorMessage }
            : { valid: true };
    }

    // 验证uTools服务
    validateUtoolsService(serviceConfig) {
        if (typeof utools === 'undefined') {
            return { valid: false, error: 'uTools API不可用，请确保在uTools环境中运行' };
        }

        if (!utools.ai) {
            return { valid: false, error: 'uTools AI API不可用，请确保uTools版本 >= 7.0.0' };
        }

        // 检查模型配置
        if (!serviceConfig.model) {
            return { valid: false, error: 'uTools AI需要选择模型版本' };
        }

        // 支持各种自定义AI模型
        return {
            valid: true,
            info: '已支持uTools自定义AI模型，系统会自动检测模型是否支持图片识别功能'
        };
    }

    // 验证AI服务
    validateAIService(serviceConfig, service) {
        // 检查API Key（utools和ocrpro除外）
        if (service !== 'utools' && service !== 'ocrpro' && !serviceConfig.apiKey) {
            const platformNames = {
                openai: 'OpenAI',
                anthropic: 'Anthropic',
                google: 'Google',
                alibaba: '阿里云百炼',
                bytedance: '火山引擎',
                custom: '自定义平台'
            };
            const platformName = platformNames[service] || service;
            return { valid: false, error: `${platformName}平台需要API Key` };
        }

        // 验证模型配置
        if (serviceConfig.useCustomModel) {
            if (!serviceConfig.customModel) {
                return { valid: false, error: '使用自定义模型时需要输入模型名称' };
            }
        } else if (!serviceConfig.model) {
            return { valid: false, error: '请选择模型版本' };
        }

        return { valid: true };
    }

    // 获取服务配置
    getServiceConfig(config) {
        const service = config.service;
        const serviceConfig = config[service] || {};

        switch (service) {
            case 'baidu':
                return {
                    apiKey: serviceConfig.apiKey,
                    secretKey: serviceConfig.secretKey,
                    type: serviceConfig.type || 'general_basic'
                };
            case 'tencent':
                return {
                    secretId: serviceConfig.secretId,
                    secretKey: serviceConfig.secretKey,
                    region: serviceConfig.region || 'ap-beijing'
                };
            case 'aliyun':
                return {
                    accessKey: serviceConfig.accessKey,
                    accessSecret: serviceConfig.accessSecret,
                    region: serviceConfig.region || 'cn-shanghai'
                };
            case 'openai':
            case 'anthropic':
            case 'google':
            case 'alibaba':
            case 'bytedance':
            case 'ocrpro':
            case 'utools':
            case 'custom':
                const finalModel = serviceConfig.useCustomModel && serviceConfig.customModel
                    ? serviceConfig.customModel
                    : serviceConfig.model || '';

                return {
                    platform: service, // 服务名就是平台名
                    model: finalModel,
                    useCustomModel: serviceConfig.useCustomModel || false,
                    customModel: serviceConfig.customModel || '',
                    apiKey: serviceConfig.apiKey || '',
                    baseUrl: serviceConfig.baseUrl || '',
                    maxTokens: serviceConfig.maxTokens || 1000
                };
            default:
                return {};
        }
    }

    // 获取识别模式配置
    getRecognitionModeConfig(mode) {
        const config = this.getConfig();
        if (!config.recognitionModes) {
            config.recognitionModes = this.defaultConfig.recognitionModes;
        }

        const modeConfig = config.recognitionModes[mode] || this.defaultConfig.recognitionModes[mode];

        // 如果配置存在但service为空字符串，返回null表示未配置
        if (modeConfig && modeConfig.service === '') {
            return {
                service: null,
                model: null,
                prompt: modeConfig.prompt
            };
        }

        return modeConfig;
    }

    // 设置识别模式配置
    setRecognitionModeConfig(mode, service, model) {
        const config = this.getConfig();
        if (!config.recognitionModes) {
            config.recognitionModes = { ...this.defaultConfig.recognitionModes };
        }

        // 保留原有的提示词配置
        const existingPrompt = config.recognitionModes[mode]?.prompt || this.defaultConfig.recognitionModes[mode]?.prompt;

        config.recognitionModes[mode] = {
            service: service || '',
            model: model || null,
            prompt: existingPrompt
        };

        // 只在模式或服务发生变化时记录日志
        const currentConfig = config.recognitionModes[mode];
        const oldService = currentConfig?.service;
        const oldModel = currentConfig?.model;


        return this.saveConfig(config);
    }

    // 获取所有识别模式配置
    getAllRecognitionModeConfigs() {
        const config = this.getConfig();
        if (!config.recognitionModes) {
            config.recognitionModes = this.defaultConfig.recognitionModes;
        }
        return config.recognitionModes;
    }

    // 获取识别模式的提示词
    getRecognitionModePrompt(mode) {
        const config = this.getConfig();
        if (!config.recognitionModes) {
            config.recognitionModes = this.defaultConfig.recognitionModes;
        }
        const modeConfig = config.recognitionModes[mode];
        return modeConfig?.prompt || this.defaultConfig.recognitionModes[mode]?.prompt || '';
    }

    // 设置识别模式的提示词
    setRecognitionModePrompt(mode, prompt) {
        const config = this.getConfig();
        if (!config.recognitionModes) {
            config.recognitionModes = this.defaultConfig.recognitionModes;
        }

        if (!config.recognitionModes[mode]) {
            config.recognitionModes[mode] = { ...this.defaultConfig.recognitionModes[mode] };
        }

        config.recognitionModes[mode].prompt = prompt;
        return this.saveConfig(config);
    }

    // 重置配置
    resetConfig() {
        const result = window.ocrAPI.db.remove('ocr-config');
        return result.ok;
    }

    // 导出配置
    exportConfig() {
        const config = this.getConfig();
        // 移除敏感信息
        const exportConfig = JSON.parse(JSON.stringify(config));
        
        // 清空API密钥等敏感信息
        if (exportConfig.baidu) {
            exportConfig.baidu.apiKey = '';
            exportConfig.baidu.secretKey = '';
        }
        if (exportConfig.tencent) {
            exportConfig.tencent.secretId = '';
            exportConfig.tencent.secretKey = '';
        }
        if (exportConfig.aliyun) {
            exportConfig.aliyun.accessKey = '';
            exportConfig.aliyun.accessSecret = '';
        }

        // 清空AI平台的API密钥
        const aiPlatforms = ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'custom'];
        aiPlatforms.forEach(platform => {
            if (exportConfig[platform]) {
                exportConfig[platform].apiKey = '';
            }
        });

        return JSON.stringify(exportConfig, null, 2);
    }

    // 导入配置
    importConfig(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            const currentConfig = this.getConfig();
            
            // 合并配置，保留当前的敏感信息
            const mergedConfig = {
                ...currentConfig,
                ...importedConfig,
                baidu: {
                    // 如果导入的配置中没有密钥，保留当前的
                    apiKey: importedConfig.baidu?.apiKey || currentConfig.baidu?.apiKey || '',
                    secretKey: importedConfig.baidu?.secretKey || currentConfig.baidu?.secretKey || ''
                },
                tencent: {
                    ...currentConfig.tencent,
                    ...importedConfig.tencent,
                    secretId: importedConfig.tencent?.secretId || currentConfig.tencent?.secretId || '',
                    secretKey: importedConfig.tencent?.secretKey || currentConfig.tencent?.secretKey || ''
                },
                aliyun: {
                    ...currentConfig.aliyun,
                    ...importedConfig.aliyun,
                    accessKey: importedConfig.aliyun?.accessKey || currentConfig.aliyun?.accessKey || '',
                    accessSecret: importedConfig.aliyun?.accessSecret || currentConfig.aliyun?.accessSecret || ''
                }
            };

            // 合并AI平台配置
            const aiPlatforms = ['openai', 'anthropic', 'google', 'alibaba', 'bytedance', 'ocrpro', 'utools', 'custom'];
            aiPlatforms.forEach(platform => {
                mergedConfig[platform] = {
                    ...currentConfig[platform],
                    ...importedConfig[platform],
                    // 如果导入的配置中没有API Key，保留当前的
                    apiKey: importedConfig[platform]?.apiKey || currentConfig[platform]?.apiKey || ''
                };
            });

            // 合并识别模式配置
            mergedConfig.recognitionModes = {
                ...this.defaultConfig.recognitionModes,
                ...currentConfig.recognitionModes,
                ...importedConfig.recognitionModes
            };

            // 合并服务商顺序配置
            mergedConfig.serviceOrder = importedConfig.serviceOrder || currentConfig.serviceOrder || this.defaultConfig.serviceOrder;

            return this.saveConfig(mergedConfig);
        } catch (error) {
            return { success: false, error: '配置格式错误: ' + error.message };
        }
    }

    // 获取快捷键配置
    getShortcuts() {
        const config = this.getConfig();
        return config.shortcuts || this.defaultConfig.shortcuts;
    }

    // 保存快捷键配置
    saveShortcuts(shortcuts) {
        const config = this.getConfig();
        config.shortcuts = { ...this.defaultConfig.shortcuts, ...shortcuts };
        return this.saveConfig(config);
    }



    // 检查快捷键冲突
    checkShortcutConflict(newShortcut, excludeKey = null) {
        const shortcuts = this.getShortcuts();
        for (const [key, value] of Object.entries(shortcuts)) {
            if (key !== excludeKey && value === newShortcut) {
                return key;
            }
        }
        return null;
    }

    // 格式化快捷键显示
    formatShortcut(shortcut) {
        if (!shortcut) return '';
        return shortcut.replace(/Ctrl/g, 'Ctrl').replace(/Alt/g, 'Alt').replace(/Shift/g, 'Shift');
    }

    // 获取翻译模型列表
    getTranslateModels() {
        const config = this.getConfig();
        return config.translateModels || [];
    }

    // 添加翻译模型
    addTranslateModel(service, model) {
        const config = this.getConfig();
        if (!config.translateModels) {
            config.translateModels = [];
        }

        // 检查是否已存在相同的模型
        const existingModel = config.translateModels.find(m =>
            m.service === service && m.model === model
        );

        if (existingModel) {
            return { success: false, error: '该模型已存在' };
        }

        // 添加新模型
        config.translateModels.push({
            service: service,
            model: model,
            addedAt: Date.now()
        });

        return this.saveConfig(config);
    }

    // 删除翻译模型
    removeTranslateModel(service, model) {
        const config = this.getConfig();
        if (!config.translateModels) {
            return { success: true, config: config };
        }

        // 过滤掉要删除的模型
        config.translateModels = config.translateModels.filter(m =>
            !(m.service === service && m.model === model)
        );

        return this.saveConfig(config);
    }

    // 清空翻译模型列表
    clearTranslateModels() {
        const config = this.getConfig();
        config.translateModels = [];
        return this.saveConfig(config);
    }

    // 重新排序翻译模型
    reorderTranslateModels(fromIndex, toIndex) {
        const config = this.getConfig();
        if (!config.translateModels || config.translateModels.length === 0) {
            return { success: false, error: '翻译模型列表为空' };
        }

        if (fromIndex < 0 || fromIndex >= config.translateModels.length ||
            toIndex < 0 || toIndex >= config.translateModels.length) {
            return { success: false, error: '索引超出范围' };
        }

        // 执行数组重排序
        const models = [...config.translateModels];
        const [movedModel] = models.splice(fromIndex, 1);
        models.splice(toIndex, 0, movedModel);

        config.translateModels = models;
        return this.saveConfig(config);
    }

    // 重新排序AI平台模型
    reorderPlatformModels(platform, fromIndex, toIndex) {
        const config = this.getConfig();
        if (!config[platform] || !config[platform].modelList) {
            return { success: false, error: `${platform}平台模型列表不存在` };
        }

        const modelList = config[platform].modelList;
        if (fromIndex < 0 || fromIndex >= modelList.length ||
            toIndex < 0 || toIndex >= modelList.length) {
            return { success: false, error: '索引超出范围' };
        }

        // 执行数组重排序
        const models = [...modelList];
        const [movedModel] = models.splice(fromIndex, 1);
        models.splice(toIndex, 0, movedModel);

        config[platform].modelList = models;
        return this.saveConfig(config);
    }

    // 获取当前翻译模型
    getCurrentTranslateModel() {
        const config = this.getConfig();
        return config.currentTranslateModel || null;
    }

    // 获取服务商顺序
    getServiceOrder() {
        const config = this.getConfig();
        const userServiceOrder = config.serviceOrder || [];
        const defaultServiceOrder = this.defaultConfig.serviceOrder;

        // 如果用户配置为空或者缺少新的服务商，使用默认配置
        if (userServiceOrder.length === 0 || !this.hasAllDefaultServices(userServiceOrder)) {
            return defaultServiceOrder;
        }

        return userServiceOrder;
    }

    // 检查服务商列表是否包含所有默认服务商
    hasAllDefaultServices(serviceOrder) {
        const defaultServices = this.defaultConfig.serviceOrder;
        return defaultServices.every(service => serviceOrder.includes(service));
    }

    // 重新排序服务商
    // 此操作会影响：
    // - 模型服务页面中服务商列表的显示顺序
    // - 所有模型筛选功能中的服务商排序
    // - 识别模式配置中的模型选择顺序
    // - 翻译模型添加时的模型列表显示顺序
    reorderServices(fromIndex, toIndex) {
        const config = this.getConfig();
        const serviceOrder = config.serviceOrder || [...this.defaultConfig.serviceOrder];

        if (fromIndex < 0 || fromIndex >= serviceOrder.length ||
            toIndex < 0 || toIndex >= serviceOrder.length) {
            return { success: false, error: '索引超出范围' };
        }

        // 执行数组重排序
        const services = [...serviceOrder];
        const [movedService] = services.splice(fromIndex, 1);
        services.splice(toIndex, 0, movedService);

        config.serviceOrder = services;
        return this.saveConfig(config);
    }

    // 设置当前翻译模型
    setCurrentTranslateModel(service, model, type = null) {
        const config = this.getConfig();
        config.currentTranslateModel = {
            service: service,
            model: model,
            setAt: Date.now()
        };

        // 如果指定了类型，添加类型标记
        if (type) {
            config.currentTranslateModel.type = type;
        }

        return this.saveConfig(config);
    }

    // 清除当前翻译模型
    clearCurrentTranslateModel() {
        const config = this.getConfig();
        delete config.currentTranslateModel;
        return this.saveConfig(config);
    }

    // 验证传统翻译API配置
    validateTraditionalTranslateAPI(service) {
        const config = this.getConfig();
        const serviceConfig = config[service];
        
        if (!serviceConfig) {
            return { valid: false, error: `${service}服务未配置` };
        }

        const validators = {
            baidu: () => {
                // 百度翻译使用独立的API配置
                const missing = [];
                if (!serviceConfig.translateApiKey) missing.push('翻译API Key');
                if (!serviceConfig.translateSecretKey) missing.push('翻译Secret Key');
                return missing.length > 0 
                    ? { valid: false, error: `百度翻译需要${missing.join('和')}` }
                    : { valid: true };
            },
            tencent: () => {
                // 腾讯翻译使用OCR的密钥
                const missing = [];
                if (!serviceConfig.secretId) missing.push('Secret ID');
                if (!serviceConfig.secretKey) missing.push('Secret Key');
                return missing.length > 0 
                    ? { valid: false, error: `腾讯翻译需要${missing.join('和')}（使用OCR配置）` }
                    : { valid: true };
            },
            aliyun: () => {
                // 阿里云翻译使用OCR的密钥
                const missing = [];
                if (!serviceConfig.accessKey) missing.push('Access Key');
                if (!serviceConfig.accessSecret) missing.push('Access Secret');
                return missing.length > 0 
                    ? { valid: false, error: `阿里云翻译需要${missing.join('和')}（使用OCR配置）` }
                    : { valid: true };
            }
        };

        const validator = validators[service];
        return validator ? validator() : { valid: false, error: '不支持的翻译服务' };
    }

    // 获取传统翻译API配置
    getTraditionalTranslateConfig(service) {
        const config = this.getConfig();
        const serviceConfig = config[service];
        
        if (!serviceConfig) {
            return null;
        }

        switch (service) {
            case 'baidu':
                // 百度翻译使用独立的API配置
                if (!serviceConfig.translateApiKey || !serviceConfig.translateSecretKey) {
                    return null;
                }
                return {
                    apiKey: serviceConfig.translateApiKey,
                    secretKey: serviceConfig.translateSecretKey,
                    termIds: serviceConfig.translateTermIds || '' // 术语库ID（可选）
                };
            case 'tencent':
                // 腾讯翻译使用OCR的密钥
                if (!serviceConfig.secretId || !serviceConfig.secretKey) {
                    return null;
                }
                return {
                    secretId: serviceConfig.secretId,
                    secretKey: serviceConfig.secretKey,
                    region: serviceConfig.translateRegion || serviceConfig.region || 'ap-beijing',
                    termRepoIDList: serviceConfig.translateTermRepoIDList || [],
                    sentRepoIDList: serviceConfig.translateSentRepoIDList || [],
                    untranslatedText: serviceConfig.translateUntranslatedText || ''
                };
            case 'aliyun':
                // 阿里云翻译使用OCR的密钥
                if (!serviceConfig.accessKey || !serviceConfig.accessSecret) {
                    return null;
                }
                return {
                    accessKey: serviceConfig.accessKey,
                    accessSecret: serviceConfig.accessSecret,
                    region: serviceConfig.translateRegion || serviceConfig.region || 'cn-shanghai',
                    context: serviceConfig.translateContext || ''
                };
            default:
                return null;
        }
    }

    // 保存传统翻译API配置
    saveTraditionalTranslateConfig(service, translateConfig) {
        const config = this.getConfig();
        
        if (!config[service]) {
            config[service] = {};
        }

        switch (service) {
            case 'baidu':
                // 百度翻译使用独立配置
                config[service].translateApiKey = translateConfig.apiKey || '';
                config[service].translateSecretKey = translateConfig.secretKey || '';
                config[service].translateTermIds = translateConfig.termIds || ''; // 术语库ID（可选）
                break;
            case 'tencent':
                // 腾讯翻译只需要保存地域配置和额外参数，密钥使用OCR的
                config[service].translateRegion = translateConfig.region || 'ap-beijing';
                config[service].translateTermRepoIDList = translateConfig.termRepoIDList || [];
                config[service].translateSentRepoIDList = translateConfig.sentRepoIDList || [];
                config[service].translateUntranslatedText = translateConfig.untranslatedText || '';
                break;
            case 'aliyun':
                // 阿里云翻译只需要保存地域配置和额外参数，密钥使用OCR的
                config[service].translateRegion = translateConfig.region || 'cn-shanghai';
                config[service].translateContext = translateConfig.context || '';
                break;
        }

        return this.saveConfig(config);
    }

    // 获取可用的传统翻译服务列表，按服务商顺序排序
    getAvailableTraditionalTranslateServices() {
        const config = this.getConfig();
        const services = [];

        // 获取服务商顺序配置
        const serviceOrder = this.getServiceOrder();
        const traditionalServices = ['baidu', 'tencent', 'aliyun'];

        // 按照配置的服务商顺序处理传统翻译服务
        serviceOrder.forEach(serviceName => {
            if (traditionalServices.includes(serviceName)) {
                const serviceConfig = config[serviceName];
                if (serviceConfig) {
                    const validation = this.validateTraditionalTranslateAPI(serviceName);
                    if (validation.valid) {
                        services.push({
                            service: serviceName,
                            name: this.getServiceDisplayName(serviceName),
                            type: 'traditional'
                        });
                    }
                }
            }
        });

        return services;
    }

    // 获取服务显示名称（用于翻译服务）
    getServiceDisplayName(service) {
        const names = {
            baidu: '百度智能云翻译',
            tencent: '腾讯云翻译',
            aliyun: '阿里云翻译',
            openai: 'OpenAI',
            anthropic: 'Anthropic',
            google: 'Gemini',
            alibaba: '阿里云百炼',
            bytedance: '火山引擎',
            ocrpro: 'OCR Pro',
            utools: 'uTools AI'
        };
        return names[service] || service;
    }
}

// 导出配置管理器
window.ConfigManager = ConfigManager;
