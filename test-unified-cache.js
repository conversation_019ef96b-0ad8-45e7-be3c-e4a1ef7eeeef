/**
 * OCR Pro 统一模型列表缓存管理机制测试脚本
 * 
 * 测试内容：
 * 1. 统一存储管理器的模型缓存功能
 * 2. 按需加载机制
 * 3. 手动刷新机制
 * 4. 缓存状态查询
 * 5. 降级策略
 */

// 模拟测试环境
function setupTestEnvironment() {
    console.log('=== 设置测试环境 ===');
    
    // 模拟 utools 环境
    if (typeof window === 'undefined') {
        global.window = {};
    }
    
    // 模拟 utools.dbStorage
    window.utools = {
        dbStorage: {
            setItem: function(key, value) {
                this._storage = this._storage || {};
                this._storage[key] = value;
                return true;
            },
            getItem: function(key) {
                this._storage = this._storage || {};
                return this._storage[key] || null;
            },
            removeItem: function(key) {
                this._storage = this._storage || {};
                delete this._storage[key];
                return true;
            }
        }
    };
    
    console.log('✓ 测试环境设置完成');
}

// 测试统一存储管理器
function testUnifiedStorageManager() {
    console.log('\n=== 测试统一存储管理器 ===');
    
    try {
        // 创建统一存储管理器实例
        const storage = new UnifiedStorageManager();
        window.unifiedStorage = storage;
        
        console.log('✓ 统一存储管理器创建成功');
        
        // 测试模型缓存设置
        const testModels = [
            { id: 'gpt-4', name: 'GPT-4' },
            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
        ];
        
        const success = storage.setModelListCache('openai', testModels, 'test-config');
        console.log(`✓ 模型缓存设置: ${success ? '成功' : '失败'}`);
        
        // 测试模型缓存获取
        const cached = storage.getModelListCache('openai', 'test-config');
        console.log(`✓ 模型缓存获取: ${cached ? `成功 (${cached.length} 个模型)` : '失败'}`);
        
        // 测试缓存状态查询
        const status = storage.getModelCacheStatus();
        console.log('✓ 缓存状态查询:', status);
        
        // 测试缓存清除
        const cleared = storage.clearModelListCache('openai', 'test-config');
        console.log(`✓ 缓存清除: ${cleared ? '成功' : '失败'}`);
        
        return true;
    } catch (error) {
        console.error('✗ 统一存储管理器测试失败:', error);
        return false;
    }
}

// 测试模型管理器集成
function testModelManagerIntegration() {
    console.log('\n=== 测试模型管理器集成 ===');
    
    try {
        // 创建模型管理器实例
        const modelManager = new ModelManager();
        
        console.log('✓ 模型管理器创建成功');
        
        // 测试配置哈希生成
        const hash1 = modelManager.generateConfigHash('test-key', 'test-url');
        const hash2 = modelManager.generateConfigHash('test-key', 'test-url');
        const hash3 = modelManager.generateConfigHash('different-key', 'test-url');
        
        console.log(`✓ 配置哈希生成: 相同配置哈希一致 ${hash1 === hash2 ? '✓' : '✗'}`);
        console.log(`✓ 配置哈希生成: 不同配置哈希不同 ${hash1 !== hash3 ? '✓' : '✗'}`);
        
        // 测试缓存清除方法
        const cleared = modelManager.clearAllCache();
        console.log(`✓ 清除所有缓存: ${typeof cleared === 'boolean' ? '成功' : '失败'}`);
        
        return true;
    } catch (error) {
        console.error('✗ 模型管理器集成测试失败:', error);
        return false;
    }
}

// 测试按需加载机制
function testOnDemandLoading() {
    console.log('\n=== 测试按需加载机制 ===');
    
    try {
        const storage = window.unifiedStorage;
        
        // 清除所有缓存
        storage.clearModelListCache('openai');
        
        // 模拟首次访问（应该没有缓存）
        let cached = storage.getModelListCache('openai');
        console.log(`✓ 首次访问缓存检查: ${cached === null ? '正确 (无缓存)' : '错误 (有缓存)'}`);
        
        // 模拟设置缓存
        const models = [
            { id: 'gpt-4', name: 'GPT-4' },
            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
        ];
        storage.setModelListCache('openai', models);
        
        // 模拟再次访问（应该有缓存）
        cached = storage.getModelListCache('openai');
        console.log(`✓ 再次访问缓存检查: ${cached && cached.length === 2 ? '正确 (有缓存)' : '错误 (无缓存)'}`);
        
        return true;
    } catch (error) {
        console.error('✗ 按需加载机制测试失败:', error);
        return false;
    }
}

// 测试手动刷新机制
function testManualRefresh() {
    console.log('\n=== 测试手动刷新机制 ===');
    
    try {
        const storage = window.unifiedStorage;
        
        // 设置初始缓存
        const initialModels = [
            { id: 'gpt-4', name: 'GPT-4' }
        ];
        storage.setModelListCache('openai', initialModels);
        
        let cached = storage.getModelListCache('openai');
        console.log(`✓ 初始缓存设置: ${cached && cached.length === 1 ? '成功' : '失败'}`);
        
        // 模拟手动刷新（清除缓存）
        storage.clearModelListCache('openai');
        cached = storage.getModelListCache('openai');
        console.log(`✓ 手动刷新清除缓存: ${cached === null ? '成功' : '失败'}`);
        
        // 设置新的缓存
        const newModels = [
            { id: 'gpt-4', name: 'GPT-4' },
            { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' },
            { id: 'gpt-4-turbo', name: 'GPT-4 Turbo' }
        ];
        storage.setModelListCache('openai', newModels);
        
        cached = storage.getModelListCache('openai');
        console.log(`✓ 刷新后新缓存: ${cached && cached.length === 3 ? '成功' : '失败'}`);
        
        return true;
    } catch (error) {
        console.error('✗ 手动刷新机制测试失败:', error);
        return false;
    }
}

// 测试多平台缓存
function testMultiPlatformCache() {
    console.log('\n=== 测试多平台缓存 ===');
    
    try {
        const storage = window.unifiedStorage;
        
        // 设置不同平台的缓存
        const platforms = ['openai', 'google', 'anthropic', 'alibaba', 'bytedance'];
        
        platforms.forEach((platform, index) => {
            const models = Array.from({ length: index + 1 }, (_, i) => ({
                id: `${platform}-model-${i + 1}`,
                name: `${platform.toUpperCase()} Model ${i + 1}`
            }));
            
            storage.setModelListCache(platform, models);
        });
        
        // 验证各平台缓存
        let allSuccess = true;
        platforms.forEach((platform, index) => {
            const cached = storage.getModelListCache(platform);
            const expected = index + 1;
            const success = cached && cached.length === expected;
            console.log(`✓ ${platform} 平台缓存: ${success ? '正确' : '错误'} (期望 ${expected}, 实际 ${cached ? cached.length : 0})`);
            if (!success) allSuccess = false;
        });
        
        // 测试缓存状态查询
        const status = storage.getModelCacheStatus();
        console.log('✓ 多平台缓存状态:', Object.keys(status).length > 0 ? '正确' : '错误');
        
        return allSuccess;
    } catch (error) {
        console.error('✗ 多平台缓存测试失败:', error);
        return false;
    }
}

// 运行所有测试
function runAllTests() {
    console.log('🚀 开始 OCR Pro 统一模型列表缓存管理机制测试\n');
    
    setupTestEnvironment();
    
    const tests = [
        { name: '统一存储管理器', fn: testUnifiedStorageManager },
        { name: '模型管理器集成', fn: testModelManagerIntegration },
        { name: '按需加载机制', fn: testOnDemandLoading },
        { name: '手动刷新机制', fn: testManualRefresh },
        { name: '多平台缓存', fn: testMultiPlatformCache }
    ];
    
    let passed = 0;
    let failed = 0;
    
    tests.forEach(test => {
        try {
            const result = test.fn();
            if (result) {
                passed++;
                console.log(`\n✅ ${test.name} 测试通过`);
            } else {
                failed++;
                console.log(`\n❌ ${test.name} 测试失败`);
            }
        } catch (error) {
            failed++;
            console.log(`\n❌ ${test.name} 测试异常:`, error.message);
        }
    });
    
    console.log(`\n📊 测试结果: ${passed} 通过, ${failed} 失败`);
    console.log(`🎯 测试完成率: ${Math.round(passed / tests.length * 100)}%`);
    
    if (failed === 0) {
        console.log('\n🎉 所有测试通过！统一模型列表缓存管理机制工作正常。');
    } else {
        console.log('\n⚠️  部分测试失败，请检查实现。');
    }
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    // 等待页面加载完成后运行测试
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runAllTests);
    } else {
        runAllTests();
    }
} else {
    // Node.js 环境
    runAllTests();
}
