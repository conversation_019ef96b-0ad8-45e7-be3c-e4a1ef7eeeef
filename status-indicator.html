<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR Status Indicator</title>
    <script src="https://unpkg.com/@lottiefiles/dotlottie-wc@0.6.2/dist/dotlottie-wc.js" type="module"></script>
    <style>
        /* ========================================
           CSS 变量定义
        ======================================== */
        :root {
            /* 状态颜色 */
            --success-color: #16a34a;
            --error-color: #dc2626;
            --warning-color: #d97706;
        }

        /* 暗色主题适配 */
        @media (prefers-color-scheme: dark) {
            :root {
                --success-color: #22c55e;
                --error-color: #ef4444;
                --warning-color: #fbbf24;
            }

            .status-container {
                background: rgba(30, 30, 30, 0.25) !important;
                border-color: rgba(255, 255, 255, 0.05) !important;
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2) !important;
            }

            .status-container:hover {
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5) !important;
            }
        }

        /* ========================================
           基础样式重置
        ======================================== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html,
        body {
            width: 48px;
            height: 48px;
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: transparent;
            border-radius: 12px;
            clip-path: inset(0 round 12px);
            -webkit-clip-path: inset(0 round 12px);
        }

        body {
            position: relative;
            user-select: none;
            -webkit-user-select: none;
            pointer-events: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* ========================================
           主容器样式
        ======================================== */
        .status-container {
            /* 布局 */
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            margin: 8px;

            /* 外观 */
            background: rgba(255, 255, 255, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            clip-path: inset(0 round 8px);
            -webkit-clip-path: inset(0 round 8px);

            /* 动画 */
            transition: all 0.3s ease;
        }

        .status-container:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        /* ========================================
           状态图标样式
        ======================================== */
        .status-icon {
            /* 布局 */
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;

            /* 外观 */
            background: transparent;
            border: none;
            border-radius: 0;

            /* 动画 */
            transition: all 0.3s ease;
        }

        /* 加载状态 */
        .status-loading {
            width: 24px;
            height: 24px;
        }

        .status-loading dotlottie-wc {
            /* 布局 */
            display: block;
            width: 24px !important;
            height: 24px !important;
            margin: 0 auto;

            /* 滤镜增强 */
            filter: contrast(2.5) brightness(1.6) saturate(2.8);
            -webkit-filter: contrast(2.5) brightness(1.6) saturate(2.8);
        }

        /* 成功状态 */
        .status-success {
            animation: success-pop 0.5s ease-out;
        }

        .status-success::before {
            content: '✓';
            color: var(--success-color);
            font-size: 14px;
            font-weight: bold;
            line-height: 1;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* 错误状态 */
        .status-error {
            animation: error-shake 0.5s ease-out;
        }

        .status-error::before {
            content: '✕';
            color: var(--error-color);
            font-size: 12px;
            font-weight: bold;
            line-height: 1;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* 警告状态 */
        .status-warning {
            animation: warning-bounce 0.5s ease-out;
        }

        .status-warning::before {
            content: '!';
            color: var(--warning-color);
            font-size: 14px;
            font-weight: bold;
            line-height: 1;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* ========================================
           动画定义
        ======================================== */
        .hidden {
            display: none;
        }

        .fade-in {
            animation: fade-in 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .fade-out {
            animation: fade-out 0.3s ease-out forwards;
        }

        @keyframes success-pop {
            0% {
                transform: scale(0.5);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes error-shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
            20%, 40%, 60%, 80% { transform: translateX(2px); }
        }

        @keyframes warning-bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-4px); }
            60% { transform: translateY(-2px); }
        }

        @keyframes fade-in {
            from {
                opacity: 0;
                transform: scale(0.6) translateY(10px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes fade-out {
            from {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
            to {
                opacity: 0;
                transform: scale(0.8) translateY(-5px);
            }
        }
    </style>
</head>
<body>
    <div class="status-container">
        <div id="statusIcon" class="status-icon hidden"></div>
    </div>

    <script>
        /**
         * 状态指示器类
         * 负责管理状态显示和动画效果
         */
        class StatusIndicator {
            constructor() {
                this.statusIcon = document.getElementById('statusIcon');
                this.currentStatus = null;
                this.hideTimeout = null;

                // 监听来自主进程的消息
                if (typeof window !== 'undefined' && window.electronAPI) {
                    window.electronAPI.onStatusUpdate(this.updateStatus.bind(this));
                }
            }

            /**
             * 更新状态显示
             * @param {string} status - 状态类型: loading, success, error, warning, hidden
             * @param {boolean} autoHide - 是否自动隐藏
             * @param {number} hideDelay - 隐藏延迟时间(ms)
             */
            updateStatus(status, autoHide = true, hideDelay = 2000) {
                // 清除之前的隐藏定时器
                if (this.hideTimeout) {
                    clearTimeout(this.hideTimeout);
                    this.hideTimeout = null;
                }

                // 重置状态类
                this.statusIcon.className = 'status-icon';

                if (status === 'hidden') {
                    this.hide();
                    return;
                }

                // 显示图标
                this.statusIcon.classList.remove('hidden');
                this.statusIcon.classList.add('fade-in');

                // 设置对应的状态
                this.setStatusType(status);
                this.currentStatus = status;

                console.log(`[状态指示器] 状态更新: ${status}`);
            }

            /**
             * 设置状态类型
             * @param {string} status - 状态类型
             */
            setStatusType(status) {
                switch (status) {
                    case 'loading':
                        this.statusIcon.classList.add('status-loading');
                        this.createLottieAnimation();
                        break;
                    case 'success':
                        this.statusIcon.classList.add('status-success');
                        this.statusIcon.innerHTML = '';
                        break;
                    case 'error':
                        this.statusIcon.classList.add('status-error');
                        this.statusIcon.innerHTML = '';
                        break;
                    case 'warning':
                        this.statusIcon.classList.add('status-warning');
                        this.statusIcon.innerHTML = '';
                        break;
                    default:
                        console.warn('未知状态:', status);
                        return;
                }
            }

            /**
             * 隐藏状态指示器
             */
            hide() {
                if (!this.statusIcon.classList.contains('hidden')) {
                    this.statusIcon.classList.add('fade-out');
                    setTimeout(() => {
                        this.statusIcon.classList.add('hidden');
                        this.statusIcon.classList.remove('fade-out', 'fade-in');
                        this.currentStatus = null;
                        this.closeWindow();
                    }, 300);
                }
            }

            /**
             * 显示状态指示器
             */
            show() {
                if (this.currentStatus) {
                    this.statusIcon.classList.remove('hidden');
                    this.statusIcon.classList.add('fade-in');
                }
            }

            /**
             * 获取当前状态
             * @returns {string|null} 当前状态
             */
            getCurrentStatus() {
                return this.currentStatus;
            }

            /**
             * 关闭窗口
             */
            closeWindow() {
                try {
                    console.log('[状态指示器] 尝试关闭窗口');

                    // 发送关闭信号给主进程
                    if (window.parent && window.parent !== window) {
                        try {
                            window.parent.postMessage({ type: 'close-status-indicator' }, '*');
                        } catch (e) {
                            console.warn('[状态指示器] 无法发送关闭信号');
                        }
                    }

                    // 尝试直接关闭
                    if (typeof window.close === 'function') {
                        window.close();
                    }
                } catch (error) {
                    console.error('[状态指示器] 关闭窗口失败:', error);
                }
            }

            /**
             * 创建 Lottie 动画
             */
            createLottieAnimation() {
                const lottieElement = document.createElement('dotlottie-wc');
                lottieElement.src = 'https://lottie.host/25fdc108-f48a-452d-b343-acabf3e23e87/JBNNkJ6AN8.lottie';
                lottieElement.speed = '1';
                lottieElement.autoplay = true;
                lottieElement.loop = true;

                // 设置样式
                lottieElement.style.cssText = `
                    width: 24px !important;
                    height: 24px !important;
                    display: block;
                    margin: 0 auto;
                `;

                // 添加到容器
                this.statusIcon.innerHTML = '';
                this.statusIcon.appendChild(lottieElement);

                console.log('[状态指示器] Lottie 动画已创建');
            }
        }

        /**
         * 从 URL 参数获取初始状态
         * @returns {Object} 包含状态参数的对象
         */
        function getUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                status: urlParams.get('status') || 'loading',
                autoHide: urlParams.get('autoHide') === 'true',
                hideDelay: parseInt(urlParams.get('hideDelay')) || 2000
            };
        }

        // ========================================
        // 初始化和事件监听
        // ========================================

        // 创建状态指示器实例
        const statusIndicator = new StatusIndicator();
        window.statusIndicator = statusIndicator;

        // 页面加载完成后显示初始状态
        window.addEventListener('load', () => {
            const params = getUrlParams();
            console.log('[状态指示器] 页面加载完成，初始参数:', params);
            statusIndicator.updateStatus(params.status, params.autoHide, params.hideDelay);
        });

        // 监听跨窗口消息
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'status-update') {
                const { status, autoHide, hideDelay } = event.data;
                statusIndicator.updateStatus(status, autoHide, hideDelay);
            }
        });
    </script>
</body>
</html>
