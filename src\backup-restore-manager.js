/**
 * 数据备份与恢复管理器
 * 提供完整的插件数据备份和恢复功能
 */
class BackupRestoreManager {
    constructor() {
        this.version = '1.0.0';
        this.backupFormat = 'OCR_PRO_BACKUP';
        
        // 定义需要备份的数据项
        this.backupItems = {
            // 核心配置数据（utools.db）
            db: [
                'ocr-config',
                'ocrpro-quota'
            ],
            // 历史记录和用户数据（utools.dbStorage）
            dbStorage: [
                'ocr_histories',
                'translateHistory',
                // 个人中心用户信息
                'userInfo',
                'utoolsUserInfo',
                'utoolsUserInfoInitialized',
                // 使用统计数据
                'usageStats',
                // 个人设置
                'personalSettings',
                // 语言设置
                'translateLanguageSettings'
            ],
            // 可选的缓存数据（用户可选择是否包含）
            cache: [
                'ocr_service_status_cache',
                'ocr_model_status_cache',
                'ocr_model_status_cache_backup',
                'google_fetched_models',
                'openai_fetched_models',
                'anthropic_fetched_models',
                'alibaba_fetched_models',
                'bytedance_fetched_models',
                // 模型获取时间戳
                'google_models_fetch_time',
                'openai_models_fetch_time',
                'anthropic_models_fetch_time',
                'alibaba_models_fetch_time',
                'bytedance_models_fetch_time'
            ]
        };
        
        this.callbacks = {
            onProgress: null,
            onSuccess: null,
            onError: null
        };
    }

    /**
     * 创建完整备份
     * @param {Object} options 备份选项
     * @param {boolean} options.includeConfig 是否包含配置数据
     * @param {boolean} options.includePersonal 是否包含个人使用信息
     * @param {boolean} options.includeHistory 是否包含历史记录
     * @param {boolean} options.includeCache 是否包含缓存数据
     * @returns {Promise<Object>} 备份数据对象
     */
    async createBackup(options = {}) {
        const {
            includeConfig = true,
            includePersonal = true,
            includeHistory = true,
            includeCache = true
        } = options;

        try {
            this.updateProgress('开始创建备份...', 0);

            const backupData = {
                format: this.backupFormat,
                version: this.version,
                timestamp: new Date().toISOString(),
                metadata: {
                    pluginVersion: this.getPluginVersion(),
                    utoolsVersion: this.getUtoolsVersion(),
                    includeConfig,
                    includePersonal,
                    includeHistory,
                    includeCache
                },
                data: {}
            };

            // 计算总项目数
            let totalItems = 0;
            if (includeConfig) totalItems += this.backupItems.db.length;
            if (includePersonal || includeHistory) {
                // 个人使用信息和历史记录都在dbStorage中，需要分别计算
                const personalDataKeys = ['userInfo', 'usageStats', 'personalSettings', 'utoolsUserInfo', 'utoolsUserInfoInitialized', 'translateLanguageSettings'];
                const historyDataKeys = ['ocr_histories', 'translateHistory'];

                if (includePersonal) totalItems += personalDataKeys.length;
                if (includeHistory) totalItems += historyDataKeys.length + this.backupItems.dbStorage.filter(key => !personalDataKeys.includes(key) && !historyDataKeys.includes(key)).length;
            }
            if (includeCache) totalItems += this.backupItems.cache.length;
            
            let processedItems = 0;

            // 备份核心配置数据（utools.db）
            if (includeConfig) {
                this.updateProgress('备份核心配置数据...', 10);
                backupData.data.db = {};
                for (const key of this.backupItems.db) {
                    try {
                        const data = await this.getDbData(key);
                        if (data) {
                            backupData.data.db[key] = data;
                        }
                        processedItems++;
                        this.updateProgress(`备份配置: ${key}`, 10 + (processedItems / totalItems) * 20);
                    } catch (error) {
                        console.warn(`备份配置项 ${key} 失败:`, error);
                    }
                }
            }

            // 初始化dbStorage数据容器
            if (includePersonal || includeHistory) {
                backupData.data.dbStorage = {};
            }

            // 备份个人使用信息数据
            if (includePersonal) {
                this.updateProgress('备份个人使用信息...', 30);
                const personalDataKeys = ['userInfo', 'usageStats', 'personalSettings', 'utoolsUserInfo', 'utoolsUserInfoInitialized', 'translateLanguageSettings'];

                for (const key of personalDataKeys) {
                    try {
                        const data = await this.getDbStorageData(key);
                        if (data) {
                            backupData.data.dbStorage[key] = data;
                        }
                        processedItems++;
                        this.updateProgress(`备份个人信息: ${key}`, 30 + (processedItems / totalItems) * 20);
                    } catch (error) {
                        console.warn(`备份个人信息 ${key} 失败:`, error);
                    }
                }
            }

            // 备份历史记录数据
            if (includeHistory) {
                this.updateProgress('备份历史记录数据...', 50);
                const historyDataKeys = ['ocr_histories', 'translateHistory'];
                const personalDataKeys = ['userInfo', 'usageStats', 'personalSettings', 'utoolsUserInfo', 'utoolsUserInfoInitialized', 'translateLanguageSettings'];

                // 备份明确的历史记录项
                for (const key of historyDataKeys) {
                    try {
                        const data = await this.getDbStorageData(key);
                        if (data) {
                            backupData.data.dbStorage[key] = data;
                        }
                        processedItems++;
                        this.updateProgress(`备份历史: ${key}`, 50 + (processedItems / totalItems) * 20);
                    } catch (error) {
                        console.warn(`备份历史记录 ${key} 失败:`, error);
                    }
                }

                // 备份其他dbStorage项（排除个人信息和明确的历史记录）
                for (const key of this.backupItems.dbStorage) {
                    if (!personalDataKeys.includes(key) && !historyDataKeys.includes(key)) {
                        try {
                            const data = await this.getDbStorageData(key);
                            if (data) {
                                backupData.data.dbStorage[key] = data;
                            }
                            processedItems++;
                            this.updateProgress(`备份数据: ${key}`, 50 + (processedItems / totalItems) * 20);
                        } catch (error) {
                            console.warn(`备份数据 ${key} 失败:`, error);
                        }
                    }
                }
            }

            // 备份动态键名的数据（如首次使用标志）
            if (includePersonal || includeHistory) {
                try {
                    const dynamicKeys = await this.getDynamicKeys();
                    for (const key of dynamicKeys) {
                        try {
                            const data = await this.getDbStorageData(key);
                            if (data) {
                                backupData.data.dbStorage[key] = data;
                            }
                        } catch (error) {
                            console.warn(`备份动态键 ${key} 失败:`, error);
                        }
                    }
                } catch (error) {
                    console.warn('获取动态键失败:', error);
                }
            }

            // 备份缓存数据（可选）
            if (includeCache) {
                this.updateProgress('备份缓存数据...', 70);
                backupData.data.cache = {};
                for (const key of this.backupItems.cache) {
                    try {
                        const data = await this.getDbStorageData(key);
                        if (data) {
                            backupData.data.cache[key] = data;
                        }
                        processedItems++;
                        this.updateProgress(`备份缓存: ${key}`, 70 + (processedItems / totalItems) * 25);
                    } catch (error) {
                        console.warn(`备份缓存数据 ${key} 失败:`, error);
                    }
                }
            }

            this.updateProgress('备份完成', 100);
            
            // 验证备份数据完整性
            this.validateBackupData(backupData);
            
            if (this.callbacks.onSuccess) {
                this.callbacks.onSuccess(backupData);
            }
            
            return backupData;

        } catch (error) {
            console.error('创建备份失败:', error);
            if (this.callbacks.onError) {
                this.callbacks.onError(error);
            }
            throw error;
        }
    }

    /**
     * 恢复备份数据
     * @param {Object} backupData 备份数据对象
     * @param {Object} options 恢复选项
     * @returns {Promise<boolean>} 恢复是否成功
     */
    async restoreBackup(backupData, options = {}) {
        const {
            restoreConfig = true,
            restorePersonal = true,
            restoreHistory = true,
            restoreCache = true,
            clearExisting = false
        } = options;

        try {
            this.updateProgress('开始恢复备份...', 0);

            // 验证备份数据（简化版本，只检查基本格式）
            if (!backupData || !backupData.data) {
                throw new Error('无效的备份数据格式');
            }

            // 计算总步骤数
            let totalSteps = 0;
            if (restoreConfig && backupData.data.db) totalSteps += Object.keys(backupData.data.db).length;
            if ((restorePersonal || restoreHistory) && backupData.data.dbStorage) totalSteps += Object.keys(backupData.data.dbStorage).length;
            if (restoreCache && backupData.data.cache) totalSteps += Object.keys(backupData.data.cache).length;
            
            let processedSteps = 0;

            // 清除现有数据（如果选择）
            if (clearExisting) {
                this.updateProgress('清除现有数据...', 5);
                await this.clearExistingData();
            }

            // 恢复核心配置数据
            if (restoreConfig && backupData.data.db) {
                this.updateProgress('恢复核心配置数据...', 10);
                for (const [key, data] of Object.entries(backupData.data.db)) {
                    try {
                        await this.setDbData(key, data);
                        processedSteps++;
                        this.updateProgress(`恢复配置: ${key}`, 10 + (processedSteps / totalSteps) * 20);
                    } catch (error) {
                        console.error(`恢复配置项 ${key} 失败:`, error);
                    }
                }
            }

            // 恢复个人使用信息和历史记录数据
            if ((restorePersonal || restoreHistory) && backupData.data.dbStorage) {
                const personalDataKeys = ['userInfo', 'usageStats', 'personalSettings', 'utoolsUserInfo', 'utoolsUserInfoInitialized', 'translateLanguageSettings'];
                const historyDataKeys = ['ocr_histories', 'translateHistory'];

                if (restorePersonal) {
                    this.updateProgress('恢复个人使用信息...', 35);
                }
                if (restoreHistory) {
                    this.updateProgress('恢复历史记录数据...', 50);
                }

                for (const [key, data] of Object.entries(backupData.data.dbStorage)) {
                    const isPersonalData = personalDataKeys.includes(key) || key.startsWith('isFirstTimeUser_');
                    const isHistoryData = historyDataKeys.includes(key);

                    // 根据选项决定是否恢复该数据
                    const shouldRestore = (restorePersonal && isPersonalData) ||
                                        (restoreHistory && (isHistoryData || (!isPersonalData && !isHistoryData)));

                    if (shouldRestore) {
                        try {
                            await this.setDbStorageData(key, data);
                            processedSteps++;
                            const progressBase = restorePersonal && isPersonalData ? 35 : 50;
                            this.updateProgress(`恢复数据: ${key}`, progressBase + (processedSteps / totalSteps) * 20);
                        } catch (error) {
                            console.error(`恢复数据 ${key} 失败:`, error);
                        }
                    }
                }
            }

            // 恢复缓存数据
            if (restoreCache && backupData.data.cache) {
                this.updateProgress('恢复缓存数据...', 75);
                for (const [key, data] of Object.entries(backupData.data.cache)) {
                    try {
                        await this.setDbStorageData(key, data);
                        processedSteps++;
                        this.updateProgress(`恢复缓存: ${key}`, 75 + (processedSteps / totalSteps) * 20);
                    } catch (error) {
                        console.error(`恢复缓存数据 ${key} 失败:`, error);
                    }
                }
            }

            this.updateProgress('恢复完成', 100);
            
            if (this.callbacks.onSuccess) {
                this.callbacks.onSuccess();
            }
            
            return true;

        } catch (error) {
            console.error('恢复备份失败:', error);
            if (this.callbacks.onError) {
                this.callbacks.onError(error);
            }
            throw error;
        }
    }

    /**
     * 导出备份到文件
     * @param {Object} backupData 备份数据
     * @param {string} filename 文件名
     */
    async exportBackupToFile(backupData, filename) {
        try {
            const jsonString = JSON.stringify(backupData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            
            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename || `ocr-pro-backup-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            return true;
        } catch (error) {
            console.error('导出备份文件失败:', error);
            throw error;
        }
    }

    /**
     * 从文件导入备份
     * @param {File} file 备份文件
     * @returns {Promise<Object>} 备份数据对象
     */
    async importBackupFromFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const backupData = JSON.parse(e.target.result);
                    this.validateBackupData(backupData);
                    resolve(backupData);
                } catch (error) {
                    reject(new Error('备份文件格式无效: ' + error.message));
                }
            };
            reader.onerror = () => reject(new Error('读取备份文件失败'));
            reader.readAsText(file);
        });
    }

    // 辅助方法
    async getDbData(key) {
        if (typeof utools !== 'undefined' && utools.db) {
            return utools.db.get(key);
        }
        return null;
    }

    async setDbData(key, data) {
        if (typeof utools !== 'undefined' && utools.db) {
            // 移除_rev字段，让utools重新分配
            const { _rev, ...dataWithoutRev } = data;
            const dataToSave = { _id: key, ...dataWithoutRev };
            return utools.db.put(dataToSave);
        }
        return false;
    }

    async getDbStorageData(key) {
        if (typeof utools !== 'undefined' && utools.dbStorage) {
            return utools.dbStorage.getItem(key);
        }
        return null;
    }

    async setDbStorageData(key, data) {
        if (typeof utools !== 'undefined' && utools.dbStorage) {
            utools.dbStorage.setItem(key, data);
            return true;
        }
        return false;
    }

    validateBackupData(backupData) {
        if (!backupData || typeof backupData !== 'object') {
            throw new Error('备份数据格式无效');
        }

        if (!backupData.data) {
            throw new Error('备份数据不完整');
        }

        // 简化验证：只检查是否有数据容器
        const hasValidData = backupData.data.db || backupData.data.dbStorage || backupData.data.cache;
        if (!hasValidData) {
            throw new Error('备份文件中没有有效数据');
        }
    }

    async clearExistingData() {
        // 清除现有数据的实现
        for (const key of this.backupItems.db) {
            try {
                if (typeof utools !== 'undefined' && utools.db) {
                    utools.db.remove(key);
                }
            } catch (error) {
                console.warn(`清除数据 ${key} 失败:`, error);
            }
        }
        
        for (const key of [...this.backupItems.dbStorage, ...this.backupItems.cache]) {
            try {
                if (typeof utools !== 'undefined' && utools.dbStorage) {
                    utools.dbStorage.removeItem(key);
                }
            } catch (error) {
                console.warn(`清除数据 ${key} 失败:`, error);
            }
        }
    }

    getPluginVersion() {
        // 从plugin.json或其他地方获取插件版本
        return '1.0.0'; // 占位符
    }

    getUtoolsVersion() {
        if (typeof utools !== 'undefined' && utools.getVersion) {
            return utools.getVersion();
        }
        return 'unknown';
    }

    updateProgress(message, percentage) {
        this._triggerProgress(message, percentage);
    }

    // 设置回调函数
    setCallbacks(callbacks) {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    /**
     * 获取备份信息摘要
     * @param {Object} backupData 备份数据
     * @returns {Object} 备份信息摘要
     */
    getBackupSummary(backupData) {
        if (!backupData || !backupData.data) {
            return null;
        }

        const summary = {
            version: backupData.version,
            timestamp: backupData.timestamp,
            metadata: backupData.metadata,
            items: {
                config: 0,
                history: 0,
                cache: 0
            },
            size: {
                total: 0,
                config: 0,
                history: 0,
                cache: 0
            }
        };

        // 统计配置项
        if (backupData.data.db) {
            summary.items.config = Object.keys(backupData.data.db).length;
            summary.size.config = JSON.stringify(backupData.data.db).length;
        }

        // 统计历史记录
        if (backupData.data.dbStorage) {
            summary.items.history = Object.keys(backupData.data.dbStorage).length;
            summary.size.history = JSON.stringify(backupData.data.dbStorage).length;
        }

        // 统计缓存数据
        if (backupData.data.cache) {
            summary.items.cache = Object.keys(backupData.data.cache).length;
            summary.size.cache = JSON.stringify(backupData.data.cache).length;
        }

        summary.size.total = summary.size.config + summary.size.history + summary.size.cache;

        return summary;
    }

    /**
     * 验证当前环境是否支持备份恢复
     * @returns {Object} 验证结果
     */
    validateEnvironment() {
        const result = {
            supported: true,
            issues: [],
            warnings: []
        };

        // 检查utools API可用性
        if (typeof utools === 'undefined') {
            result.supported = false;
            result.issues.push('utools API不可用');
        } else {
            if (!utools.db) {
                result.supported = false;
                result.issues.push('utools.db API不可用');
            }
            if (!utools.dbStorage) {
                result.warnings.push('utools.dbStorage API不可用，部分功能受限');
            }
        }

        // 检查统一存储管理器
        if (typeof window.unifiedStorage === 'undefined') {
            result.warnings.push('统一存储管理器不可用，将使用原生API');
        }

        return result;
    }

    /**
     * 创建备份文件名
     * @param {Object} options 选项
     * @returns {string} 文件名
     */
    generateBackupFilename(options = {}) {
        const {
            includeCache = false,
            includeHistory = true,
            prefix = 'ocr-pro-backup'
        } = options;

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        let suffix = '';

        if (includeCache && includeHistory) {
            suffix = '-full';
        } else if (includeHistory) {
            suffix = '-with-history';
        } else {
            suffix = '-config-only';
        }

        return `${prefix}${suffix}-${timestamp}.json`;
    }

    /**
     * 比较两个备份的差异
     * @param {Object} backup1 备份1
     * @param {Object} backup2 备份2
     * @returns {Object} 差异报告
     */
    compareBackups(backup1, backup2) {
        const diff = {
            timestamp: {
                backup1: backup1.timestamp,
                backup2: backup2.timestamp
            },
            differences: {
                config: [],
                history: [],
                cache: []
            }
        };

        // 比较配置数据
        this.compareDataSection(backup1.data.db, backup2.data.db, diff.differences.config);

        // 比较历史数据
        this.compareDataSection(backup1.data.dbStorage, backup2.data.dbStorage, diff.differences.history);

        // 比较缓存数据
        this.compareDataSection(backup1.data.cache, backup2.data.cache, diff.differences.cache);

        return diff;
    }

    compareDataSection(data1, data2, diffArray) {
        const keys1 = Object.keys(data1 || {});
        const keys2 = Object.keys(data2 || {});
        const allKeys = new Set([...keys1, ...keys2]);

        for (const key of allKeys) {
            if (!data1 || !data1[key]) {
                diffArray.push({ key, type: 'added', in: 'backup2' });
            } else if (!data2 || !data2[key]) {
                diffArray.push({ key, type: 'removed', in: 'backup1' });
            } else {
                const hash1 = this.hashObject(data1[key]);
                const hash2 = this.hashObject(data2[key]);
                if (hash1 !== hash2) {
                    diffArray.push({ key, type: 'modified' });
                }
            }
        }
    }

    hashObject(obj) {
        // 简单的对象哈希函数
        return JSON.stringify(obj).split('').reduce((a, b) => {
            a = ((a << 5) - a) + b.charCodeAt(0);
            return a & a;
        }, 0);
    }

    /**
     * 获取当前数据状态摘要
     * @returns {Promise<Object>} 当前数据摘要
     */
    async getCurrentDataSummary() {
        const summary = {
            config: {},
            history: {},
            cache: {},
            timestamp: new Date().toISOString()
        };

        // 获取配置数据摘要
        for (const key of this.backupItems.db) {
            try {
                const data = await this.getDbData(key);
                if (data) {
                    summary.config[key] = {
                        exists: true,
                        size: JSON.stringify(data).length,
                        lastModified: data._rev || 'unknown'
                    };
                } else {
                    summary.config[key] = { exists: false };
                }
            } catch (error) {
                summary.config[key] = { exists: false, error: error.message };
            }
        }

        // 获取历史数据摘要
        for (const key of this.backupItems.dbStorage) {
            try {
                const data = await this.getDbStorageData(key);
                if (data) {
                    summary.history[key] = {
                        exists: true,
                        size: JSON.stringify(data).length,
                        itemCount: Array.isArray(data) ? data.length : 'N/A'
                    };
                } else {
                    summary.history[key] = { exists: false };
                }
            } catch (error) {
                summary.history[key] = { exists: false, error: error.message };
            }
        }

        // 获取动态键数据摘要
        try {
            const dynamicKeys = await this.getDynamicKeys();
            for (const key of dynamicKeys) {
                try {
                    const data = await this.getDbStorageData(key);
                    if (data) {
                        summary.history[key] = {
                            exists: true,
                            size: JSON.stringify(data).length,
                            type: 'dynamic'
                        };
                    }
                } catch (error) {
                    summary.history[key] = { exists: false, error: error.message, type: 'dynamic' };
                }
            }
        } catch (error) {
            console.warn('获取动态键摘要失败:', error);
        }

        // 获取缓存数据摘要
        for (const key of this.backupItems.cache) {
            try {
                const data = await this.getDbStorageData(key);
                if (data) {
                    summary.cache[key] = {
                        exists: true,
                        size: JSON.stringify(data).length,
                        timestamp: data._timestamp || 'unknown'
                    };
                } else {
                    summary.cache[key] = { exists: false };
                }
            } catch (error) {
                summary.cache[key] = { exists: false, error: error.message };
            }
        }

        return summary;
    }

    /**
     * 设置回调函数
     * @param {Object} callbacks 回调函数对象
     */
    setCallbacks(callbacks) {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    /**
     * 生成备份文件名
     * @param {Object} options 备份选项
     * @returns {string} 文件名
     */
    generateBackupFilename(options = {}) {
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 19).replace(/[T:]/g, '-');

        let suffix = '';
        if (options.includeHistory && options.includeCache) {
            suffix = '-full';
        } else if (options.includeHistory) {
            suffix = '-with-history';
        } else if (options.includeCache) {
            suffix = '-with-cache';
        } else {
            suffix = '-config-only';
        }

        return `ocr-pro-backup-${dateStr}${suffix}.json`;
    }

    /**
     * 获取备份数据摘要
     * @param {Object} backupData 备份数据
     * @returns {Object} 摘要信息
     */
    getBackupSummary(backupData) {
        if (!backupData || backupData.format !== this.backupFormat) {
            throw new Error('无效的备份文件格式');
        }

        const summary = {
            version: backupData.version,
            timestamp: backupData.timestamp,
            items: {
                config: 0,
                history: 0,
                cache: 0
            }
        };

        // 统计各类数据项数量
        if (backupData.data.db) {
            summary.items.config = Object.keys(backupData.data.db).length;
        }

        if (backupData.data.dbStorage) {
            summary.items.history = Object.keys(backupData.data.dbStorage).length;
        }

        if (backupData.data.cache) {
            summary.items.cache = Object.keys(backupData.data.cache).length;
        }

        return summary;
    }

    /**
     * 触发进度回调
     * @param {string} message 进度消息
     * @param {number} percentage 进度百分比
     */
    _triggerProgress(message, percentage) {
        if (this.callbacks.onProgress) {
            this.callbacks.onProgress(message, percentage);
        }
    }

    /**
     * 触发成功回调
     * @param {*} data 成功数据
     */
    _triggerSuccess(data) {
        if (this.callbacks.onSuccess) {
            this.callbacks.onSuccess(data);
        }
    }

    /**
     * 触发错误回调
     * @param {Error} error 错误对象
     */
    _triggerError(error) {
        if (this.callbacks.onError) {
            this.callbacks.onError(error);
        }
    }

    /**
     * 获取动态键名（如首次使用标志）
     * @returns {Promise<Array>} 动态键名数组
     */
    async getDynamicKeys() {
        const dynamicKeys = [];

        try {
            // 获取所有存储键（如果utools.dbStorage支持）
            if (typeof utools !== 'undefined' && utools.dbStorage) {
                // 由于utools.dbStorage没有直接的getAllKeys方法，
                // 我们需要通过已知的模式来查找动态键
                const patterns = [
                    'isFirstTimeUser_', // 首次使用标志
                ];

                // 遍历可能的键名模式
                for (const pattern of patterns) {
                    // 尝试一些常见的用户标识符格式
                    const possibleSuffixes = [
                        'default', 'user', 'anonymous',
                        // 可以根据实际情况添加更多可能的后缀
                    ];

                    for (const suffix of possibleSuffixes) {
                        const key = pattern + suffix;
                        try {
                            const data = await this.getDbStorageData(key);
                            if (data !== null && data !== undefined) {
                                dynamicKeys.push(key);
                            }
                        } catch (error) {
                            // 键不存在，继续
                        }
                    }
                }

                // 尝试获取当前用户的首次使用标志
                try {
                    // @ts-ignore - window.ocrPlugin 在运行时存在
                    if (typeof window !== 'undefined' && window.ocrPlugin && window.ocrPlugin.uiManager) {
                        // @ts-ignore
                        const userIdentifier = await window.ocrPlugin.uiManager.getUserIdentifier();
                        const firstTimeKey = `isFirstTimeUser_${userIdentifier}`;
                        const data = await this.getDbStorageData(firstTimeKey);
                        if (data !== null && data !== undefined) {
                            dynamicKeys.push(firstTimeKey);
                        }
                    }
                } catch (error) {
                    console.warn('获取用户标识符失败:', error);
                }
            }
        } catch (error) {
            console.warn('获取动态键失败:', error);
        }

        return [...new Set(dynamicKeys)]; // 去重
    }
}

// 导出管理器实例
window.backupRestoreManager = new BackupRestoreManager();
